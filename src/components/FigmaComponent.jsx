import React from 'react';
import './FigmaComponent.css';

const FigmaComponent = () => {
  return (


    <div className="figma-component-container">
      <div className="figma-card">
        <div className="figma-header">
          <h2 className="figma-title">设计系统组件</h2>
          <p className="figma-subtitle">基于Figma设计生成的React组件</p>
        </div>
        
        <div className="figma-content">
          <div className="feature-grid">
            <div className="feature-card">
              <div className="feature-icon">🎨</div>
              <h3>现代化设计</h3>
              <p>采用最新的设计系统和UI模式</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>高性能</h3>
              <p>优化的React组件，快速渲染</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🔧</div>
              <h3>可定制</h3>
              <p>灵活的样式和配置选项</p>
            </div>
          </div>
        </div>
        
        <div className="figma-footer">
          <button className="primary-button">开始使用</button>
          <button className="secondary-button">查看文档</button>
        </div>
      </div>
    </div>
  );
};

export default FigmaComponent;